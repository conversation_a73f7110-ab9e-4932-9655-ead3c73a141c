// Signature upload service for Supabase storage
import { createClient } from '@supabase/supabase-js'

// Create a separate client with service key for file uploads
const getServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceKey = process.env.SUPABASE_SERVICE_KEY
  
  if (!supabaseUrl || !serviceKey) {
    throw new Error('Missing Supabase configuration for signature upload')
  }
  
  return createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

export class SignatureService {
  /**
   * Convert data URL to Blob
   */
  private static dataURLToBlob(dataURL: string): Blob {
    const arr = dataURL.split(',')
    const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png'
    const bstr = atob(arr[1])
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n)
    }
    
    return new Blob([u8arr], { type: mime })
  }

  /**
   * Generate unique filename for signature
   */
  private static generateSignatureFilename(interviewId: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    return `signature_${interviewId}_${timestamp}.png`
  }

  /**
   * Upload signature to Supabase storage
   */
  static async uploadSignature(
    signatureDataURL: string, 
    interviewId: string
  ): Promise<string> {
    try {
      const supabase = getServiceClient()
      const bucketName = process.env.SIGNATURE_BUCKET_NAME
      
      if (!bucketName) {
        throw new Error('SIGNATURE_BUCKET_NAME environment variable is not set')
      }

      // Convert data URL to blob
      const blob = this.dataURLToBlob(signatureDataURL)
      
      // Generate unique filename
      const filename = this.generateSignatureFilename(interviewId)
      
      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(filename, blob, {
          contentType: 'image/png',
          upsert: false
        })

      if (error) {
        console.error('Supabase upload error:', error)
        throw new Error(`Failed to upload signature: ${error.message}`)
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filename)

      if (!urlData?.publicUrl) {
        throw new Error('Failed to get public URL for uploaded signature')
      }

      return urlData.publicUrl
    } catch (error) {
      console.error('Error uploading signature:', error)
      throw error
    }
  }

  /**
   * Delete signature from Supabase storage
   */
  static async deleteSignature(signatureUrl: string): Promise<void> {
    try {
      const supabase = getServiceClient()
      const bucketName = process.env.SIGNATURE_BUCKET_NAME
      
      if (!bucketName) {
        throw new Error('SIGNATURE_BUCKET_NAME environment variable is not set')
      }

      // Extract filename from URL
      const url = new URL(signatureUrl)
      const pathParts = url.pathname.split('/')
      const filename = pathParts[pathParts.length - 1]

      // Delete from storage
      const { error } = await supabase.storage
        .from(bucketName)
        .remove([filename])

      if (error) {
        console.error('Error deleting signature:', error)
        throw new Error(`Failed to delete signature: ${error.message}`)
      }
    } catch (error) {
      console.error('Error deleting signature:', error)
      throw error
    }
  }

  /**
   * Validate signature data URL
   */
  static validateSignatureData(dataURL: string): boolean {
    try {
      // Check if it's a valid data URL
      if (!dataURL.startsWith('data:image/')) {
        return false
      }

      // Check if it has base64 data
      const parts = dataURL.split(',')
      if (parts.length !== 2) {
        return false
      }

      // Try to decode base64
      atob(parts[1])
      return true
    } catch {
      return false
    }
  }
}
