import type React from "react"
import { Document, Page, Text, View, StyleSheet, pdf } from "@react-pdf/renderer"
import type { Interview, TranscriptData, Statement, Case, User } from "@/types/database"
import { INTERVIEW_ENVIRONMENT_LABELS } from "@/types/database"
import { PDFMarkdown } from "@/components/pdf_markdown_parser"

// PDF Styles (keeping your existing styles)
const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#FFFFFF",
    padding: 30,
    fontSize: 11,
    lineHeight: 1.4,
  },
  header: {
    marginBottom: 20,
    borderBottom: 1,
    borderBottomColor: "#000000",
    paddingBottom: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 12,
    textAlign: "center",
    color: "#666666",
  },
  section: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#000000",
    borderBottom: 1,
    borderBottomColor: "#CCCCCC",
    paddingBottom: 2,
  },
  row: {
    flexDirection: "row",
    marginBottom: 4,
  },
  label: {
    width: "30%",
    fontWeight: "bold",
    color: "#333333",
  },
  value: {
    width: "70%",
    color: "#000000",
  },
  transcriptContainer: {
    marginTop: 10,
  },
  transcriptSegment: {
    marginBottom: 8,
    flexDirection: "row",
  },
  speaker: {
    width: "20%",
    fontWeight: "bold",
    color: "#000000",
    paddingRight: 10,
  },
  timestamp: {
    fontSize: 9,
    color: "#666666",
    marginTop: 2,
  },
  transcriptText: {
    width: "80%",
    color: "#000000",
    lineHeight: 1.3,
  },
  statement: {
    backgroundColor: "#F8F9FA",
    padding: 10,
    marginTop: 5,
    lineHeight: 1.4,
  },
  officerNotes: {
    backgroundColor: "#F0F0F0",
    padding: 8,
    marginTop: 10,
    fontSize: 10,
    lineHeight: 1.3,
  },
  footer: {
    position: "absolute",
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: "center",
    fontSize: 9,
    color: "#666666",
    borderTop: 1,
    borderTopColor: "#CCCCCC",
    paddingTop: 10,
  },
})

interface PDFDocumentProps {
  interview: Interview
  case: Case
  officer: User
  transcriptData?: TranscriptData
  statement?: Statement
}

const InterviewPDFDocument: React.FC<PDFDocumentProps> = ({
  interview,
  case: caseData,
  officer,
  transcriptData,
  statement,
}) => {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`
    } else {
      return `${remainingSeconds}s`
    }
  }

  const getEnvironmentLabel = (environment?: string) => {
    if (!environment) return "Not specified"
    return INTERVIEW_ENVIRONMENT_LABELS[environment as keyof typeof INTERVIEW_ENVIRONMENT_LABELS] || environment
  }

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>FIRE INVESTIGATION UNIT</Text>
          <Text style={styles.subtitle}>Witness Interview Report</Text>
        </View>

        {/* Case Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Case Information</Text>
          <View style={styles.row}>
            <Text style={styles.label}>Case ID:</Text>
            <Text style={styles.value}>{caseData.id}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Incident Location:</Text>
            <Text style={styles.value}>{caseData.incidentLocation}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Incident Date:</Text>
            <Text style={styles.value}>
              {caseData.incidentDate} at {caseData.incidentTime}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Case Status:</Text>
            <Text style={styles.value}>{caseData.status}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Assigned Officer:</Text>
            <Text style={styles.value}>{caseData.assignedOfficer?.fullName || "Unknown"}</Text>
          </View>
        </View>

        {/* Interview Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Interview Information</Text>
          <View style={styles.row}>
            <Text style={styles.label}>Interview ID:</Text>
            <Text style={styles.value}>{interview.id}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Interviewing Officer:</Text>
            <Text style={styles.value}>{officer.fullName}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Badge Number:</Text>
            <Text style={styles.value}>{officer.badgeNumber || "N/A"}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Department:</Text>
            <Text style={styles.value}>{officer.department || "N/A"}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Interview Date:</Text>
            <Text style={styles.value}>
              {interview.startTime ? formatDate(interview.startTime) : formatDate(interview.createdAt)}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Start Time:</Text>
            <Text style={styles.value}>{interview.startTime ? formatTime(interview.startTime) : "Not recorded"}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>End Time:</Text>
            <Text style={styles.value}>{interview.endTime ? formatTime(interview.endTime) : "Not recorded"}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Duration:</Text>
            <Text style={styles.value}>{interview.duration ? formatDuration(interview.duration) : "Not recorded"}</Text>
          </View>
        </View>

        {/* Witness Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Witness Information</Text>
          <View style={styles.row}>
            <Text style={styles.label}>Name:</Text>
            <Text style={styles.value}>{interview.witness.name}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Type:</Text>
            <Text style={styles.value}>{interview.witness.type}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Contact:</Text>
            <Text style={styles.value}>{interview.witness.contact}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Environment:</Text>
            <Text style={styles.value}>{getEnvironmentLabel(interview.witness.environment)}</Text>
          </View>
        </View>

        {/* Statement - NOW WITH MARKDOWN FORMATTING! */}
        {statement && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Statement Summary</Text>
            <View style={styles.statement}>
              <PDFMarkdown content={statement.content} />
            </View>
          </View>
        )}

        {/* Officer Notes */}
        {statement?.officerNotes && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Officer Notes</Text>
            <View style={styles.officerNotes}>
              <Text>{statement.officerNotes}</Text>
            </View>
          </View>
        )}

        {/* Footer */}
        <Text style={styles.footer}>
          Generated on {new Date().toLocaleDateString("en-US")} at {new Date().toLocaleTimeString("en-US")} | Fire
          Investigation Unit - Witness Interview System
        </Text>
      </Page>
    </Document>
  )
}

export const generateInterviewPDF = async (
  interview: Interview,
  caseData: Case,
  officer: User,
  transcriptData?: TranscriptData,
  statement?: Statement,
): Promise<Blob> => {
  const doc = (
    <InterviewPDFDocument
      interview={interview}
      case={caseData}
      officer={officer}
      transcriptData={transcriptData}
      statement={statement}
    />
  )

  return await pdf(doc).toBlob()
}

export const downloadPDF = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob)

  // Download the file
  const link = document.createElement("a")
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // Open in new tab
  window.open(url, "_blank")

  // Clean up
  setTimeout(() => URL.revokeObjectURL(url), 100)
}
