"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { X, RotateCcw, Check } from "lucide-react"
import SignatureCanvas from "react-signature-canvas"
import type ReactSignatureCanvas from "react-signature-canvas"

interface SignatureOverlayProps {
  isOpen: boolean
  onClose: () => void
  onSave: (signatureData: string) => void
  existingSignature?: string
  title?: string
}

export function SignatureOverlay({
  isOpen,
  onClose,
  onSave,
  existingSignature,
  title = "Witness Signature"
}: SignatureOverlayProps) {
  const signatureRef = useRef<ReactSignatureCanvas>(null)
  const [hasSignature, setHasSignature] = useState(false)

  useEffect(() => {
    if (isOpen && existingSignature && signatureRef.current) {
      // Load existing signature if available
      signatureRef.current.fromDataURL(existingSignature)
      setHasSignature(true)
    }
  }, [isOpen, existingSignature])

  const handleClear = () => {
    if (signatureRef.current) {
      signatureRef.current.clear()
      setHasSignature(false)
    }
  }

  const handleSave = () => {
    if (signatureRef.current && !signatureRef.current.isEmpty()) {
      const signatureData = signatureRef.current.toDataURL()
      onSave(signatureData)
      onClose()
    }
  }

  const handleSignatureChange = () => {
    if (signatureRef.current) {
      setHasSignature(!signatureRef.current.isEmpty())
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground">
            Please sign in the area below to confirm your statement.
          </div>
          
          {/* Signature Pad Container */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
            <SignatureCanvas
              ref={signatureRef}
              canvasProps={{
                width: 600,
                height: 200,
                className: "signature-canvas bg-white rounded border"
              }}
              backgroundColor="rgb(255, 255, 255)"
              penColor="rgb(0, 0, 0)"
              minWidth={1}
              maxWidth={3}
              onEnd={handleSignatureChange}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-4">
            <Button
              variant="outline"
              onClick={handleClear}
              className="flex items-center gap-2"
              disabled={!hasSignature}
            >
              <RotateCcw className="h-4 w-4" />
              Clear
            </Button>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={onClose}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={!hasSignature}
                className="flex items-center gap-2"
              >
                <Check className="h-4 w-4" />
                Save Signature
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
