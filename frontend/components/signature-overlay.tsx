"use client"

import { useState, useRef, useEffect } from "react"
import dynamic from "next/dynamic"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { X, RotateCcw, Check } from "lucide-react"

// Dynamically import SignaturePad to avoid SSR issues
const SignaturePad = dynamic(() => import("react-signature-pad"), {
  ssr: false,
  loading: () => (
    <div className="w-full h-[200px] bg-gray-100 rounded border flex items-center justify-center">
      <p className="text-gray-500">Loading signature pad...</p>
    </div>
  )
})

interface SignatureOverlayProps {
  isOpen: boolean
  onClose: () => void
  onSave: (signatureData: string) => void
  existingSignature?: string
  title?: string
}

export function SignatureOverlay({
  isOpen,
  onClose,
  onSave,
  existingSignature,
  title = "Witness Signature"
}: SignatureOverlayProps) {
  const signaturePadRef = useRef<any>(null)
  const [hasSignature, setHasSignature] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  // Ensure component is mounted on client side
  useEffect(() => {
    setIsMounted(true)
  }, [])

  useEffect(() => {
    if (isOpen && existingSignature && signaturePadRef.current && isMounted) {
      // Load existing signature if available
      signaturePadRef.current.fromDataURL(existingSignature)
      setHasSignature(true)
    }
  }, [isOpen, existingSignature, isMounted])

  const handleClear = () => {
    if (signaturePadRef.current) {
      signaturePadRef.current.clear()
      setHasSignature(false)
    }
  }

  const handleSave = () => {
    if (signaturePadRef.current && !signaturePadRef.current.isEmpty()) {
      const signatureData = signaturePadRef.current.toDataURL()
      onSave(signatureData)
      onClose()
    }
  }

  const handleSignatureChange = () => {
    if (signaturePadRef.current) {
      setHasSignature(!signaturePadRef.current.isEmpty())
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground">
            Please sign in the area below to confirm your statement.
          </div>
          
          {/* Signature Pad Container */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
            {isMounted ? (
              <SignaturePad
                ref={signaturePadRef}
                canvasProps={{
                  width: 600,
                  height: 200,
                  className: "signature-canvas bg-white rounded border"
                }}
                options={{
                  backgroundColor: 'rgb(255, 255, 255)',
                  penColor: 'rgb(0, 0, 0)',
                  minWidth: 1,
                  maxWidth: 3,
                }}
                onEnd={handleSignatureChange}
              />
            ) : (
              <div className="w-full h-[200px] bg-white rounded border flex items-center justify-center">
                <p className="text-gray-500">Loading signature pad...</p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-4">
            <Button
              variant="outline"
              onClick={handleClear}
              className="flex items-center gap-2"
              disabled={!hasSignature || !isMounted}
            >
              <RotateCcw className="h-4 w-4" />
              Clear
            </Button>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={onClose}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={!hasSignature || !isMounted}
                className="flex items-center gap-2"
              >
                <Check className="h-4 w-4" />
                Save Signature
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
