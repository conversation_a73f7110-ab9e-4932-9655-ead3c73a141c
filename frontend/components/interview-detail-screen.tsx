"use client"

import { useState, useEffect } from "react"
import { useAtom } from 'jotai'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ArrowLeft, Loader2, User, Clock, Download, AlertCircle, Volume2, VolumeX, FileAudio } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import { TranscriptionService, StatementService } from "@/lib/supabase"
import { useToast } from "@/hooks/use-toast"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import ReactAudioPlayer from 'react-audio-player'
import { selectedInterviewAtom, selectedCaseAtom, exportDataAtom } from "@/store/atoms"

import type { Screen } from "@/app/page"
import type { Transcription, Statement, TranscriptData } from "@/types/database"
import { INTERVIEW_ENVIRONMENT_LABELS } from "@/types/database"

interface InterviewDetailScreenProps {
  onNavigate: (screen: Screen) => void
}

export function InterviewDetailScreen({ onNavigate }: InterviewDetailScreenProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [transcription, setTranscription] = useState<Transcription | null>(null)
  const [statement, setStatement] = useState<Statement | null>(null)
  const [transcriptData, setTranscriptData] = useState<TranscriptData | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isExporting, setIsExporting] = useState(false)
  const [selectedInterview] = useAtom(selectedInterviewAtom)
  const [selectedCase] = useAtom(selectedCaseAtom)
  const [, setExportData] = useAtom(exportDataAtom)

  const interview = selectedInterview

  useEffect(() => {
    if (!interview) {
      setError("No interview selected")
      setLoading(false)
      return
    }

    const fetchInterviewDetails = async () => {
      try {
        setLoading(true)
        setError(null)

        // Fetch transcription
        const transcriptionData = await TranscriptionService.getTranscription(interview.id)
        setTranscription(transcriptionData)

        if (transcriptionData) {
          const transcriptDataFormatted = await TranscriptionService.getTranscriptionData(interview.id)
          setTranscriptData(transcriptDataFormatted)
        }

        // Fetch statement
        const statementData = await StatementService.getStatement(interview.id)
        setStatement(statementData)

      } catch (err) {
        console.error('Error fetching interview details:', err)
        setError(err instanceof Error ? err.message : 'Failed to load interview details')
      } finally {
        setLoading(false)
      }
    }

    fetchInterviewDetails()
  }, [interview])

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }) + " at " + date.toLocaleTimeString("en-US")
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`
    } else {
      return `${remainingSeconds}s`
    }
  }

  const getEnvironmentLabel = (environment?: string) => {
    if (!environment) return 'Not specified'
    return INTERVIEW_ENVIRONMENT_LABELS[environment as keyof typeof INTERVIEW_ENVIRONMENT_LABELS] || environment
  }

  const handleExport = async () => {
    if (!interview || !user || !selectedCase) return

    setIsExporting(true)
    try {
      // Prepare export data for the export screen
      setExportData({
        interview: interview,
        case: selectedCase,
        officer: user,
        transcriptData: transcriptData || null,
        statement: statement || null,
      })

      toast({
        title: "Export Data Prepared",
        description: "Interview data has been prepared for export. Redirecting to export screen.",
      })

      // Navigate to export screen
      onNavigate("export-screen")

    } catch (error) {
      console.error('Error preparing export:', error)
      toast({
        title: "Export Preparation Failed",
        description: error instanceof Error ? error.message : "Failed to prepare interview for export",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  if (!interview) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-8">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">No Interview Selected</h2>
          <p className="text-gray-600 mb-4">Please select an interview to view details.</p>
          <Button onClick={() => onNavigate("case-details")}>
            Back to Case Details
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("case-details")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Case
        </Button>
        <h2 className="text-2xl font-semibold">Interview Details</h2>
        <div></div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          <span>Loading interview details...</span>
        </div>
      ) : error ? (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : (
        <div className="space-y-6">
          {/* Interview Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Interview #{interview.id.slice(-8)}</span>
                <Badge variant="default">
                  {transcription && statement ? "Complete" : transcription ? "Transcribed" : "Recorded"}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Witness Information */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                    <User className="w-4 h-4" />
                    Witness Information
                  </div>
                  <div className="pl-6 space-y-2">
                    <p><strong>Name:</strong> {interview.witness.name}</p>
                    <p><strong>Type:</strong> {interview.witness.type}</p>
                    <p><strong>Contact:</strong> {interview.witness.contact}</p>
                    <p><strong>Environment:</strong> {getEnvironmentLabel(interview.witness.environment)}</p>
                  </div>
                </div>

                {/* Interview Details */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                    <Clock className="w-4 h-4" />
                    Interview Details
                  </div>
                  <div className="pl-6 space-y-2">
                    <p><strong>Officer:</strong> {(interview as any).interviewingOfficer?.fullName || 'Unknown'}</p>
                    {(interview as any).interviewingOfficer?.badgeNumber && (
                      <p><strong>Badge:</strong> {(interview as any).interviewingOfficer.badgeNumber}</p>
                    )}
                    <p><strong>Created:</strong> {formatDate(interview.createdAt)}</p>
                    {interview.startTime && (
                      <p><strong>Started:</strong> {formatDate(interview.startTime)}</p>
                    )}
                    {interview.endTime && (
                      <p><strong>Ended:</strong> {formatDate(interview.endTime)}</p>
                    )}
                    {interview.duration && (
                      <p><strong>Duration:</strong> {formatDuration(interview.duration)}</p>
                    )}
                    <div className="flex items-center gap-2">
                      <strong>Recording:</strong>
                      {interview.recordingPath ? (
                        <div className="flex items-center gap-1 text-green-600">
                          <FileAudio className="w-4 h-4" />
                          <span className="text-sm">Available</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-1 text-gray-500">
                          <VolumeX className="w-4 h-4" />
                          <span className="text-sm">Not saved</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Audio Recording */}
          {interview.recordingPath && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Volume2 className="w-5 h-5" />
                  Interview Recording
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Audio recording from the interview session
                  </p>
                  <ReactAudioPlayer
                    src={interview.recordingPath}
                    controls
                    className="w-full"
                    preload="metadata"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Transcription */}
          {transcriptData && (
            <Card>
              <CardHeader className="bg-secondary">
                <CardTitle className="text-lg">Interview Transcript</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Recorded: {interview.startTime ? formatDate(interview.startTime) : 'Unknown'}
                </p>
              </CardHeader>
              <CardContent className="p-0">
                <div className="max-h-96 overflow-y-auto p-4">
                  {transcriptData.segments.map((segment, index) => {
                    const speaker = transcriptData.speakers.find((s) => s.id === segment.speaker)
                    return (
                      <div key={index} className="flex gap-4 mb-4">
                        <div
                          className="min-w-[120px] text-right pr-3 border-r-2 text-sm font-medium"
                          style={{ borderRightColor: speaker?.color }}
                        >
                          {speaker?.name}
                          <div className="text-xs text-muted-foreground font-normal">{segment.timestamp}</div>
                        </div>
                        <div className="flex-1 text-sm leading-relaxed">{segment.text}</div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Statement */}
          {statement && (
            <Card>
              <CardHeader className="bg-secondary">
                <CardTitle className="text-lg">Statement Summary</CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <div className="markdown-content">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    components={{
                      h1: ({ children }) => (
                        <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100">{children}</h1>
                      ),
                      h2: ({ children }) => (
                        <h2 className="text-xl font-semibold mb-3 mt-6 text-gray-900 dark:text-gray-100">{children}</h2>
                      ),
                      h3: ({ children }) => (
                        <h3 className="text-lg font-medium mb-2 mt-4 text-gray-900 dark:text-gray-100">{children}</h3>
                      ),
                      p: ({ children }) => (
                        <p className="mb-3 text-gray-700 dark:text-gray-300 leading-relaxed">{children}</p>
                      ),
                      ul: ({ children }) => <ul className="list-disc pl-6 mb-4 space-y-1">{children}</ul>,
                      li: ({ children }) => <li className="text-gray-700 dark:text-gray-300">{children}</li>,
                      strong: ({ children }) => (
                        <strong className="font-semibold text-gray-900 dark:text-gray-100">{children}</strong>
                      ),
                    }}
                  >
                    {statement.content}
                  </ReactMarkdown>
                </div>
                {statement.officerNotes && (
                  <div className="mt-4 pt-4 border-t">
                    <h4 className="font-medium text-sm text-gray-700 mb-2">Officer Notes:</h4>
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        p: ({ children }) => (
                          <p className="mb-2 text-gray-600 dark:text-gray-300 leading-relaxed">{children}</p>
                        ),
                      }}
                    >
                      {statement.officerNotes}
                    </ReactMarkdown>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Export Button */}
          <div className="flex justify-center">
            <Button 
              size="lg" 
              onClick={handleExport}
              disabled={isExporting || !transcription || !statement}
              className="min-w-[200px]"
            >
              {isExporting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Preparing Export...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Prepare Export
                </>
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
